import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../data/models/astrology/chart_type.dart';
import '../../data/models/astrology/planet_position.dart';
import '../../presentation/viewmodels/chart_viewmodel.dart';
import '../../presentation/viewmodels/settings_viewmodel.dart';
import 'painter/chart_painter.dart';
import 'painter/dual_chart_painter.dart';
import 'painter/firdaria_chart_painter.dart';

class ZoomableChartWidget extends StatefulWidget {
  final ChartViewModel viewModel;
  final Function(PlanetPosition)? onPlanetTap;
  final Function(dynamic)? onFirdariaTap;

  const ZoomableChartWidget({
    Key? key,
    required this.viewModel,
    this.onPlanetTap,
    this.onFirdariaTap,
  }) : super(key: key);

  @override
  State<ZoomableChartWidget> createState() => ZoomableChartWidgetState();
}

class ZoomableChartWidgetState extends State<ZoomableChartWidget> {
  // 緩存 CustomPainter 實例
  CustomPainter? _cachedPainter;

  /// 根據圖表類型決定使用哪種繪製器
  CustomPainter _getChartPainter() {
    // 檢查是否需要重新創建繪製器（設定變更時）
    if (_cachedPainter != null && _shouldRecreateChartPainter()) {
      print('ZoomableChartWidget._getChartPainter: 設定已變更，清除緩存');
      _cachedPainter = null;
    }

    // if (_cachedPainter != null) return _cachedPainter!;

    final chartType = widget.viewModel.chartType;

    // 如果是法達盤，使用 FirdariaChartPainter
    if (chartType == ChartType.firdaria) {
      final firdariaData = widget.viewModel.firdariaData ?? [];
      final birthDate = widget.viewModel.chartData.primaryPerson.dateTime;
      final isDaytime = widget.viewModel.isDaytimeBirth;
      final currentDate = widget.viewModel.specificDate;

      _cachedPainter = FirdariaChartPainter(
        widget.viewModel.chartData.planets!,
        widget.viewModel.chartData.aspects!,
        housesData: widget.viewModel.chartData.houses!,
        chartType: chartType,
        firdariaData: firdariaData,
        birthDate: birthDate,
        isDaytime: isDaytime,
        selectedPeriodIndex: widget.viewModel.selectedFirdariaPeriodIndex,
        currentDate: currentDate,
      );
      return _cachedPainter!;
    }

    // 需要雙圈顯示的圖表類型
    if (_needsDualChartPainter()) {
      if (chartType == ChartType.synastry ||
          chartType == ChartType.synastrySecondary ||
          chartType == ChartType.synastryTertiary) {
        _cachedPainter = DualChartPainter(
          widget.viewModel.chartData.primaryPerson.planets!,
          widget.viewModel.chartData.secondaryPerson!.planets!,
          widget.viewModel.chartData.aspects!,
          housesData: widget.viewModel.chartData.houses!,
          chartType: chartType,
          chartSettings: widget.viewModel.settingsViewModel?.chartSettings,
        );
      } else if (chartType == ChartType.transit) {
        _cachedPainter = DualChartPainter(
          widget.viewModel.chartData.primaryPerson.planets!,
          widget.viewModel.chartData.planets!,
          widget.viewModel.chartData.aspects!,
          housesData: widget.viewModel.chartData.houses!,
          chartType: chartType,
          chartSettings: widget.viewModel.settingsViewModel?.chartSettings,
        );
      } else {
        _cachedPainter = DualChartPainter(
          widget.viewModel.chartData.primaryPerson.planets!,
          widget.viewModel.chartData.planets!,
          widget.viewModel.chartData.aspects!,
          housesData: widget.viewModel.chartData.houses!,
          chartType: chartType,
          chartSettings: widget.viewModel.settingsViewModel?.chartSettings,
        );
      }
    } else {
      _cachedPainter = ChartPainter(
        widget.viewModel.chartData,
        widget.viewModel.chartData.planets!,
        widget.viewModel.chartData.aspects!,
        housesData: widget.viewModel.chartData.houses!,
        chartType: chartType,
        chartSettings: widget.viewModel.settingsViewModel?.chartSettings,
      );
    }

    return _cachedPainter!;
  }

  bool _needsDualChartPainter() {
    final chartType = widget.viewModel.chartType;
    final progressionTypes = [ChartType.solarArcDirection];
    final specialDualTypes = [ChartType.transit, ChartType.synastry];

    return progressionTypes.contains(chartType) ||
        specialDualTypes.contains(chartType) ||
        chartType.isSynastryProgression;
  }

  void _handleChartTap(Offset tapPosition) {
    // 不需要調整座標偏移，因為 tapPosition 已經是相對於 CustomPaint 的座標
    // CustomPaint 的尺寸已經是 chartSize - 64，所以座標是正確的
    final adjustedPosition = tapPosition;

    final CustomPainter painter = _getChartPainter();
    painter.hitTest(adjustedPosition);

    // 處理法達盤點擊
    if (painter is FirdariaChartPainter) {
      final hitPeriod = painter.getLastHitPeriod();
      if (hitPeriod != null && widget.onFirdariaTap != null) {
        widget.onFirdariaTap!(hitPeriod);
        return;
      }
    }

    // 處理行星點擊
    PlanetPosition? hitPlanet;
    if (painter is ChartPainter) {
      hitPlanet = painter.getLastHitPlanet();
    } else if (painter is DualChartPainter) {
      hitPlanet = painter.getLastHitPlanet();
    } else if (painter is FirdariaChartPainter) {
      hitPlanet = painter.getLastHitPlanet();
    }

    if (hitPlanet != null && widget.onPlanetTap != null) {
      widget.onPlanetTap!(hitPlanet);
    }
  }

  void _clearCache() {
    _cachedPainter = null;
  }

  /// 檢查是否需要重新創建星盤繪製器
  bool _shouldRecreateChartPainter() {
    // 如果沒有緩存的繪製器，需要創建
    if (_cachedPainter == null) return true;

    // 檢查設定是否變更
    final currentSettings = widget.viewModel.settingsViewModel?.chartSettings;
    if (currentSettings == null) return false;

    // 檢查度數顯示設定是否變更
    if (_cachedPainter is ChartPainter) {
      final cachedPainter = _cachedPainter as ChartPainter;
      final cachedSettings = cachedPainter.chartSettings;

      if (cachedSettings == null && currentSettings != null) return true;
      if (cachedSettings != null && currentSettings == null) return true;
      if (cachedSettings == null && currentSettings == null) return false;

      // 檢查關鍵設定是否變更
      return cachedSettings!.showHouseDegrees !=
              currentSettings.showHouseDegrees ||
          cachedSettings.showPlanetDegrees !=
              currentSettings.showPlanetDegrees ||
          cachedSettings.showZodiacRulers != currentSettings.showZodiacRulers;
    }

    // 對於其他類型的繪製器，也進行類似檢查
    if (_cachedPainter is DualChartPainter) {
      final cachedPainter = _cachedPainter as DualChartPainter;
      final cachedSettings = cachedPainter.chartSettings;

      if (cachedSettings == null && currentSettings != null) return true;
      if (cachedSettings != null && currentSettings == null) return true;
      if (cachedSettings == null && currentSettings == null) return false;

      return cachedSettings!.showHouseDegrees !=
              currentSettings.showHouseDegrees ||
          cachedSettings.showPlanetDegrees !=
              currentSettings.showPlanetDegrees ||
          cachedSettings.showZodiacRulers != currentSettings.showZodiacRulers;
    }

    return false;
  }

  @override
  void didUpdateWidget(ZoomableChartWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.viewModel.chartType != widget.viewModel.chartType ||
        oldWidget.viewModel.chartData != widget.viewModel.chartData) {
      _clearCache();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.viewModel.chartData.planets == null ||
        widget.viewModel.chartData.houses == null) {
      return const Center(child: CircularProgressIndicator());
    }

    // 使用 Consumer 監聽 SettingsViewModel 的變化
    return Consumer<SettingsViewModel>(
      builder: (context, settingsViewModel, child) {
        // 獲取容器的實際大小
        return LayoutBuilder(
          builder: (context, constraints) {
            final size = Size(constraints.maxWidth, constraints.maxHeight);
            final chartSize =
                size.width < size.height ? size.width : size.height;

            return Stack(
              children: [
                // 固定大小的星盤 - 完全置中，符合螢幕寬度
                Center(
                  child: Container(
                    padding: const EdgeInsets.all(0), // 最小內邊距，最大化星盤尺寸
                    child: Container(
                      width: chartSize,
                      height: chartSize,
                      decoration: const BoxDecoration(
                        color: Colors.transparent, // 透明背景，不遮擋星盤
                      ),
                      child: GestureDetector(
                        onTapDown: (details) =>
                            _handleChartTap(details.localPosition),
                        child: CustomPaint(
                          painter: _getChartPainter(),
                          size: Size(chartSize, chartSize),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
