# 出生時間不確定UI提示功能

## 🎯 功能概述

為AI解讀結果頁面添加出生時間不確定的UI提示功能，當出生時間標記為不確定時，會在界面上顯示相關警告和說明，幫助用戶了解對分析結果的影響。

## 📋 功能需求

### 原始需求
用戶希望在AI解讀結果頁面上：
1. 顯示出生時間不確定的注意事項
2. 在文案旁邊有問號圖標
3. 點擊問號可以了解對分析有什麼影響

### 後端邏輯
在 `chart_interpretation_service.dart` 中已有相關邏輯：
```dart
if (chartData.primaryPerson.isTimeUncertain) {
  buffer.writeln("主要人物的出生時間不確定，不要涉及時間相關的因素。");
}
if (chartData.secondaryPerson != null &&
    chartData.secondaryPerson!.isTimeUncertain) {
  buffer.writeln("次要人物的出生時間不確定，不要涉及時間相關的因素。");
}
```

## 🛠️ 實現方案

### 1. 用戶資訊區域優化

#### 出生時間顯示改進
- **原來**：直接顯示時間
- **現在**：使用 `_buildTimeInfoRow()` 方法
  - 顯示時間
  - 如果時間不確定，在旁邊顯示橙色問號圖標
  - 點擊問號顯示說明對話框

#### 警告區域
- 在出生資訊下方添加警告區域
- 使用橙色背景的警告卡片
- 包含警告圖標、文字說明和問號圖標

### 2. 星盤資料卡片優化

#### 人物資訊顯示
- 在主要人物和次要人物名稱後添加 "(時間不確定)" 標記
- 在星盤資訊底部添加整體警告區域

#### 智能警告文字
- 根據不同情況顯示不同的警告文字：
  - 僅主要人物時間不確定
  - 僅次要人物時間不確定  
  - 兩位人物時間都不確定

### 3. 說明對話框

#### 對話框內容
- **標題**：出生時間不確定的影響
- **影響項目**：
  - 上升星座（需要精確的出生時間）
  - 宮位分析（宮位邊界會因時間而變化）
  - 月亮星座（如果時間差距較大）
  - 行星宮位位置
  - 相位的精確度
- **建議**：
  - 盡量確認準確的出生時間
  - 重點關注太陽星座和主要行星位置
  - 參考分析時保持適當的彈性
- **注意事項**：AI分析已自動調整，會避免過度依賴時間相關的因素

## 📝 代碼實現

### 新增方法

#### 1. `_buildTimeInfoRow(BirthData birthData)`
構建包含問號圖標的出生時間資訊行

#### 2. `_buildTimeUncertaintyWarning()`
構建用戶資訊區域的時間不確定警告

#### 3. `_hasTimeUncertainty()`
檢查是否有時間不確定的情況

#### 4. `_buildChartTimeUncertaintyWarning()`
構建星盤資料卡片的時間不確定警告

#### 5. `_showTimeUncertaintyDialog()`
顯示出生時間不確定說明對話框

### 修改的方法

#### 1. `_buildUserInfoSection()`
- 替換原有的出生時間顯示
- 添加時間不確定警告區域

#### 2. `_buildEnhancedChartDataCard()`
- 在星盤資訊底部添加警告區域

#### 3. `_buildPrimaryPersonInfo()` 和 `_buildSecondaryPersonInfo()`
- 在人物名稱後添加時間不確定標記

## 🎨 UI設計

### 視覺元素
- **顏色主題**：使用橙色 (Colors.orange) 表示警告
- **圖標**：
  - `Icons.warning_amber_rounded` - 警告圖標
  - `Icons.help_outline` - 問號圖標
- **背景**：橙色半透明背景 (alpha: 0.1)
- **邊框**：橙色半透明邊框 (alpha: 0.3)

### 交互設計
- **點擊區域**：問號圖標有明確的點擊區域
- **視覺回饋**：問號圖標有背景圓圈
- **對話框**：使用標準的 AlertDialog 樣式

## 🔄 功能流程

### 1. 頁面載入
1. 檢查 `birthData.isTimeUncertain` 或 `chartData.primaryPerson.isTimeUncertain`
2. 如果為 true，顯示相關的警告UI元素

### 2. 用戶交互
1. 用戶看到橙色警告區域和問號圖標
2. 點擊問號圖標
3. 彈出說明對話框
4. 用戶閱讀說明後點擊「了解」關閉對話框

### 3. AI分析調整
- 後端已自動在prompt中添加相關指示
- AI會避免過度依賴時間相關的分析因素

## 📊 適用場景

### 個人分析
- 單人星盤分析時，顯示該人的時間不確定警告

### 關係分析
- 雙人星盤分析時，可能顯示：
  - 僅一方時間不確定
  - 雙方時間都不確定
- 智能調整警告文字內容

### 其他星盤類型
- 適用於所有包含出生時間的星盤類型
- 包括本命盤、合盤、流年等

## 🚀 技術優勢

### 1. 用戶體驗
- **直觀提示**：橙色警告色彩明確傳達注意事項
- **詳細說明**：點擊問號獲得完整的影響說明
- **智能適配**：根據不同情況顯示相應的警告內容

### 2. 代碼品質
- **模組化設計**：每個UI元素都有獨立的構建方法
- **可重用性**：警告組件可以在其他頁面中重用
- **維護性**：清晰的方法命名和結構

### 3. 一致性
- **設計統一**：與應用整體的警告樣式保持一致
- **行為統一**：所有問號圖標都有相同的交互行為
- **文字統一**：警告文字與後端邏輯保持一致

## 📈 未來擴展

### 可能的改進
1. **動畫效果**：添加警告區域的淡入動畫
2. **個性化設定**：允許用戶隱藏已讀的警告
3. **更多說明**：添加如何獲得準確出生時間的指導
4. **視覺優化**：根據用戶回饋調整顏色和布局

### 其他頁面應用
- 星盤頁面也可以添加類似的警告提示
- 出生資料列表可以顯示時間不確定的標記
- 星盤設定頁面可以提供時間校正功能

## ✅ 測試確認

### 編譯測試
- ✅ Flutter分析通過，無語法錯誤
- ✅ 僅有少量代碼風格警告，不影響功能

### 功能測試建議
1. **單人時間不確定**：測試個人分析頁面的警告顯示
2. **雙人時間不確定**：測試關係分析頁面的警告顯示
3. **問號點擊**：測試對話框的彈出和內容顯示
4. **響應式設計**：測試在不同螢幕尺寸下的顯示效果

這個功能為用戶提供了清晰的時間不確定提示，幫助他們更好地理解和使用AI分析結果。
